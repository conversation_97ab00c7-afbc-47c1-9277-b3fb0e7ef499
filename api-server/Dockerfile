FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy solution file and all project files for proper restore
COPY *.sln ./
COPY api-server/*.csproj ./api-server/
COPY worker-service/*.csproj ./worker-service/
COPY shared/*.csproj ./shared/

# Restore dependencies for the entire solution
RUN dotnet restore

# Copy all source code
COPY api-server/ ./api-server/
COPY shared/ ./shared/

# Build and publish the API server
WORKDIR /app/api-server
RUN dotnet publish -c Release -o out --no-restore

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app

# Install FFmpeg and other runtime dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libgdiplus \
    libc6-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy the published application
COPY --from=build /app/api-server/out .

# Cloud Run expects the app to listen on the PORT environment variable
EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

ENTRYPOINT ["dotnet", "api-server.dll"]
