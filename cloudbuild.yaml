steps:
  # Build API Server
  - name: 'gcr.io/cloud-builders/docker'
    args: 
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/vidcompressor-api-server:latest'
      - '-f'
      - 'api-server/Dockerfile'
      - '.'
    id: 'build-api-server'

  # Build Worker Service
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/vidcompressor-worker-service:latest'
      - '-f'
      - 'worker-service/Dockerfile'
      - '.'
    id: 'build-worker-service'

  # Push API Server image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
    waitFor: ['build-api-server']
    id: 'push-api-server'

  # Push Worker Service image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
    waitFor: ['build-worker-service']
    id: 'push-worker-service'

# Store images in Container Registry
images:
  - 'gcr.io/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/vidcompressor-api-server:latest'
  - 'gcr.io/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/vidcompressor-worker-service:latest'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'
