steps:
  # Build API Server
  - name: 'gcr.io/cloud-builders/docker'
    args: 
      - 'build'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-api-server:latest'
      - '-f'
      - 'api-server/Dockerfile'
      - '.'
    id: 'build-api-server'

  # Build Worker Service
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-worker-service:latest'
      - '-f'
      - 'worker-service/Dockerfile'
      - '.'
    id: 'build-worker-service'

  # Push API Server image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
    waitFor: ['build-api-server']
    id: 'push-api-server'

  # Push Worker Service image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
    waitFor: ['build-worker-service']
    id: 'push-worker-service'

  # Deploy API Server to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vidcompressor-api-server'
      - '--image'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
      - '--region'
      - 'us-central1'  # Change to your preferred region
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'  # Remove if you want authentication
    waitFor: ['push-api-server']
    id: 'deploy-api-server'

  # Deploy Worker Service to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vidcompressor-worker-service'
      - '--image'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
      - '--region'
      - 'us-central1'  # Change to your preferred region
      - '--platform'
      - 'managed'
      - '--no-allow-unauthenticated'  # Worker service typically shouldn't be public
    waitFor: ['push-worker-service']
    id: 'deploy-worker-service'

# Store images in Container Registry
images:
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-api-server:$COMMIT_SHA'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-api-server:latest'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-worker-service:$COMMIT_SHA'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor-worker-service:latest'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'
