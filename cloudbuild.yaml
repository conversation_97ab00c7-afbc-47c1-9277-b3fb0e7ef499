steps:
  # Install Node.js dependencies for frontend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['ci']
    dir: 'frontend'
    id: 'install-frontend-deps'

  # Build frontend
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    dir: 'frontend'
    waitFor: ['install-frontend-deps']
    id: 'build-frontend'

  # Deploy to Firebase Hosting
  - name: 'node:18'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        set -e
        echo "Installing Firebase CLI..."
        npm install -g firebase-tools
        echo "Firebase CLI installed successfully"
        echo "Current directory: $(pwd)"
        echo "Listing frontend build directory:"
        ls -la frontend/build/ || echo "Frontend build directory not found"
        echo "Firebase configuration:"
        cat firebase.json || echo "Firebase config not found"
        echo "Starting Firebase deployment..."
        firebase deploy --only hosting --token $$FIREBASE_TOKEN --project $$PROJECT_ID
        echo "Firebase deployment completed"
    secretEnv: ['FIREBASE_TOKEN']
    waitFor: ['build-frontend']
    id: 'deploy-frontend'

  # Build API Server
  - name: 'gcr.io/cloud-builders/docker'
    args: 
      - 'build'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:latest'
      - '-f'
      - 'api-server/Dockerfile'
      - '.'
    id: 'build-api-server'

  # Build Worker Service (can run in parallel with frontend)
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
      - '-t'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:latest'
      - '-f'
      - 'worker-service/Dockerfile'
      - '.'
    id: 'build-worker-service'

  # Push API Server image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
    waitFor: ['build-api-server']
    id: 'push-api-server'

  # Push Worker Service image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
    waitFor: ['build-worker-service']
    id: 'push-worker-service'

  # Deploy API Server to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vidcompressor-api-server'
      - '--image'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
      - '--region'
      - 'us-central1'  # Change to your preferred region
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'  # Remove if you want authentication
    waitFor: ['push-api-server']
    id: 'deploy-api-server'

  # Deploy Worker Service to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vidcompressor-worker-service'
      - '--image'
      - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
      - '--region'
      - 'us-central1'  # Change to your preferred region
      - '--platform'
      - 'managed'
      - '--no-allow-unauthenticated'  # Worker service typically shouldn't be public
    waitFor: ['push-worker-service']
    id: 'deploy-worker-service'

# Store images in Artifact Registry
images:
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:$COMMIT_SHA'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/api-server:latest'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:$COMMIT_SHA'
  - 'us-east4-docker.pkg.dev/$PROJECT_ID/vidcompressor/worker-service:latest'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: '100'
  logging: CLOUD_LOGGING_ONLY

# Timeout for the entire build
timeout: '1200s'

# Secrets for Firebase deployment
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/firebase-token/versions/latest
      env: 'FIREBASE_TOKEN'
