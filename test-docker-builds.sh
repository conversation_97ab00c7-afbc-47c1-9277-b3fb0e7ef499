#!/bin/bash

# Test Docker builds for both services
echo "Testing Docker builds..."

echo "Building API Server..."
if docker build -t vidcompressor-api-server -f api-server/Dockerfile .; then
    echo "✅ API Server build successful"
else
    echo "❌ API Server build failed"
    exit 1
fi

echo "Building Worker Service..."
if docker build -t vidcompressor-worker-service -f worker-service/Dockerfile .; then
    echo "✅ Worker Service build successful"
else
    echo "❌ Worker Service build failed"
    exit 1
fi

echo "🎉 All Docker builds completed successfully!"

# Optional: Test with docker-compose
echo "Testing docker-compose build..."
if docker-compose build; then
    echo "✅ Docker Compose build successful"
else
    echo "❌ Docker Compose build failed"
    exit 1
fi

echo "🎉 All builds completed successfully!"
