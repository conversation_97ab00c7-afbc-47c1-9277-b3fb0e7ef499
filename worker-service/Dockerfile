FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy project files for restore
COPY worker-service/*.csproj ./worker-service/
COPY shared/*.csproj ./shared/

# Restore dependencies
WORKDIR /app/worker-service
RUN dotnet restore

# Copy source code
WORKDIR /app
COPY worker-service/ ./worker-service/
COPY shared/ ./shared/

# Build and publish
WORKDIR /app/worker-service
RUN dotnet publish -c Release -o out

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/worker-service/out .

# Cloud Run expects the app to listen on the PORT environment variable
EXPOSE 8080
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Production

ENTRYPOINT ["dotnet", "worker-service.dll"]
